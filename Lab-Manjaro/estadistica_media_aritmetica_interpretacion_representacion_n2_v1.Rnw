\documentclass[10pt,a4paper]{article}

%% paquetes basicos
\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{a4wide,color,verbatim,Sweave,url,xargs,amsmath,booktabs,longtable}
\usepackage{graphicx,float}
\usepackage{enumitem}

%% entornos para exams
\newenvironment{question}{\item}{}
\newenvironment{solution}{\comment}{\endcomment}
\newenvironment{answerlist}{\renewcommand{\labelenumii}{(\alph{enumii})}\begin{enumerate}}{\end{enumerate}}

%% comandos para metadatos exams
\newcommand{\exname}[1]{\def\@exname{#1}}
\newcommand{\extype}[1]{\def\@extype{#1}}
\newcommand{\exsolution}[1]{\def\@exsolution{#1}}
\newcommand{\exshuffle}[1]{\def\@exshuffle{#1}}
\newcommand{\exsection}[1]{\def\@exsection{#1}}

%% configuracion parrafos
\setlength{\parskip}{0.7ex plus0.1ex minus0.1ex}
\setlength{\parindent}{0em}

\begin{document}
\SweaveOpts{concordance=TRUE}

\begin{enumerate}

<<echo=FALSE, results=hide>>=
# Configuracion inicial
Sys.setlocale("LC_ALL", "C")
options(OutDec = ".")

# CONFIGURACION RADICAL ANTI-NOTACION CIENTIFICA
options(scipen = 999)
options(digits = 10)

# Librerias esenciales
library(exams)
library(digest)
library(testthat)

# Semilla aleatoria
set.seed(sample(1:100000, 1))

# Funcion para formatear numeros sin notacion cientifica
formatear_numero <- function(numero, decimales = 1) {
  formatC(numero, format = "f", digits = decimales, big.mark = "")
}

# Funcion generar_datos()
generar_datos <- function() {
  # Contextos variados para la pregunta
  contextos <- list(
    list(
      materia = "Matematicas",
      actividad = "examenes",
      periodo = "bimestre",
      estudiante = sample(c("Ana", "Carlos", "Maria", "Luis", "Sofia", "Diego", "Laura", "Miguel"), 1)
    ),
    list(
      materia = "Ciencias Naturales",
      actividad = "laboratorios",
      periodo = "trimestre",
      estudiante = sample(c("Camila", "Andres", "Valentina", "Santiago", "Isabella", "Sebastian", "Gabriela", "Nicolas"), 1)
    ),
    list(
      materia = "Espanol",
      actividad = "ensayos",
      periodo = "periodo academico",
      estudiante = sample(c("Alejandra", "Daniel", "Natalia", "Felipe", "Mariana", "Alejandro", "Carolina", "Mateo"), 1)
    ),
    list(
      materia = "Historia",
      actividad = "evaluaciones",
      periodo = "semestre",
      estudiante = sample(c("Juliana", "Juan", "Andrea", "David", "Paola", "Cristian", "Melissa", "Ricardo"), 1)
    )
  )
  
  contexto_seleccionado <- sample(contextos, 1)[[1]]
  
  # Generar numero de calificaciones (entre 4 y 6)
  num_calificaciones <- sample(4:6, 1)
  
  # Generar calificaciones realistas (entre 2.5 y 5.0)
  calificaciones <- round(runif(num_calificaciones, 2.5, 5.0), 1)
  
  # Calcular la media aritmetica correcta
  media_correcta <- round(mean(calificaciones), 1)
  
  # SISTEMA AVANZADO DE DISTRACTORES
  # 30% probabilidad de valores duplicados con justificaciones diferentes
  permitir_valores_duplicados <- sample(c(TRUE, FALSE), 1, prob = c(0.3, 0.7))
  
  # Generar distractores diversos
  distractores_valores <- c()
  
  # Distractor 1: Confundir con mediana
  mediana_calculada <- round(median(calificaciones), 1)
  if(mediana_calculada != media_correcta) {
    distractores_valores <- c(distractores_valores, mediana_calculada)
  }
  
  # Distractor 2: Error de calculo (suma sin dividir)
  suma_total <- sum(calificaciones)
  if(suma_total != media_correcta && suma_total <= 10) {
    distractores_valores <- c(distractores_valores, suma_total)
  }
  
  # Distractor 3: Usar solo la primera y ultima calificacion
  promedio_extremos <- round((calificaciones[1] + calificaciones[length(calificaciones)]) / 2, 1)
  if(promedio_extremos != media_correcta) {
    distractores_valores <- c(distractores_valores, promedio_extremos)
  }
  
  # Distractor 4: Valor maximo
  valor_maximo <- max(calificaciones)
  if(valor_maximo != media_correcta) {
    distractores_valores <- c(distractores_valores, valor_maximo)
  }
  
  # Distractor 5: Valor minimo
  valor_minimo <- min(calificaciones)
  if(valor_minimo != media_correcta) {
    distractores_valores <- c(distractores_valores, valor_minimo)
  }
  
  # Distractor 6: Error de redondeo
  media_sin_redondear <- mean(calificaciones)
  media_mal_redondeada <- round(media_sin_redondear + 0.2, 1)
  if(media_mal_redondeada != media_correcta && media_mal_redondeada <= 5.0) {
    distractores_valores <- c(distractores_valores, media_mal_redondeada)
  }
  
  # Seleccionar 3 distractores unicos
  distractores_valores <- unique(distractores_valores)
  if(length(distractores_valores) >= 3) {
    distractores_seleccionados <- sample(distractores_valores, 3)
  } else {
    # Completar con valores aleatorios si no hay suficientes
    while(length(distractores_valores) < 3) {
      nuevo_valor <- round(runif(1, 2.0, 5.0), 1)
      if(!(nuevo_valor %in% c(distractores_valores, media_correcta))) {
        distractores_valores <- c(distractores_valores, nuevo_valor)
      }
    }
    distractores_seleccionados <- distractores_valores[1:3]
  }
  
  # Crear opciones con justificaciones
  opciones <- c()
  explicaciones <- c()
  
  # Opcion correcta
  opciones[1] <- paste0(formatear_numero(media_correcta, 1),
                       " porque se suman todas las calificaciones y se divide por el numero total de evaluaciones")
  explicaciones[1] <- "Correcto. La media aritmetica se calcula sumando todos los valores y dividiendo por la cantidad de datos."

  # Distractores con justificaciones incorrectas
  opciones[2] <- paste0(formatear_numero(distractores_seleccionados[1], 1),
                       " porque representa el valor central de las calificaciones ordenadas")
  explicaciones[2] <- "Incorrecto. Esta es la definicion de mediana, no de media aritmetica."

  opciones[3] <- paste0(formatear_numero(distractores_seleccionados[2], 1),
                       " porque es el resultado de sumar todas las calificaciones")
  explicaciones[3] <- "Incorrecto. Falta dividir la suma por el numero de calificaciones."

  opciones[4] <- paste0(formatear_numero(distractores_seleccionados[3], 1),
                       " porque es el promedio entre la calificacion mas alta y la mas baja")
  explicaciones[4] <- "Incorrecto. La media aritmetica considera todas las calificaciones, no solo los extremos."
  
  # Determinar respuesta correcta y mezclar opciones
  solutions <- c(TRUE, FALSE, FALSE, FALSE)
  orden_aleatorio <- sample(1:4)
  opciones <- opciones[orden_aleatorio]
  explicaciones <- explicaciones[orden_aleatorio]
  solutions <- solutions[orden_aleatorio]
  
  return(list(
    contexto = contexto_seleccionado,
    calificaciones = calificaciones,
    num_calificaciones = num_calificaciones,
    media_correcta = media_correcta,
    opciones = opciones,
    explicaciones = explicaciones,
    solutions = solutions,
    suma_total = suma_total
  ))
}

# Generar datos para este ejercicio
datos <- generar_datos()

# Prueba de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  versiones <- list()
  for(i in 1:1000) {
    datos_test <- generar_datos()
    versiones[[i]] <- digest::digest(datos_test)
  }
  
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 300,
              info = paste("Solo se generaron", n_versiones_unicas,
                          "versiones unicas. Se requieren al menos 300."))
})
@

\begin{question}

En el \Sexpr{datos$contexto$periodo} de \Sexpr{datos$contexto$materia}, \Sexpr{datos$contexto$estudiante} obtuvo las siguientes calificaciones en \Sexpr{datos$num_calificaciones} \Sexpr{datos$contexto$actividad}:

\Sexpr{paste(formatear_numero(datos$calificaciones, 1), collapse = ", ")}

¿Cual es la media aritmetica de estas calificaciones?

<<echo=FALSE, results=tex>>=
answerlist(datos$opciones)
@

\end{question}

\begin{solution}

Para calcular la media aritmetica de las calificaciones de \Sexpr{datos$contexto$estudiante}, debemos:

1. Sumar todas las calificaciones: \Sexpr{paste(formatear_numero(datos$calificaciones, 1), collapse = " + ")} = \Sexpr{formatear_numero(datos$suma_total, 1)}

2. Dividir la suma por el numero de calificaciones: \Sexpr{formatear_numero(datos$suma_total, 1)} $\div$ \Sexpr{datos$num_calificaciones} = \Sexpr{formatear_numero(datos$media_correcta, 1)}

Por lo tanto, la media aritmetica es \Sexpr{formatear_numero(datos$media_correcta, 1)}.

<<echo=FALSE, results=tex>>=
answerlist(datos$explicaciones)
@

\end{solution}

%% META-INFORMATION
\exname{Media Aritmetica Calificaciones}
\extype{schoice}
\exsolution{\Sexpr{mchoice2string(datos$solutions)}}
\exshuffle{TRUE}
\exsection{Estadistica}

\end{enumerate}
\end{document}
