```{r data generation, echo = FALSE, results = "hide"}
## DATA GENERATION
# Configuracion inicial
Sys.setlocale("LC_ALL", "C")
options(OutDec = ".")
options(scipen = 999)

# Generar numero de calificaciones (entre 4 y 6)
num_calificaciones <- sample(4:6, 1)

# Generar calificaciones realistas (entre 2.5 y 5.0)
calificaciones <- round(runif(num_calificaciones, 2.5, 5.0), 1)

# Calcular la media aritmetica correcta
media_correcta <- round(mean(calificaciones), 1)
suma_total <- sum(calificaciones)

# Contexto aleatorio
estudiante <- sample(c("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia", "Diego"), 1)
materia <- sample(c("Matematicas", "Ciencias", "Espanol"), 1)

## QUESTION/ANSWER GENERATION
# Generar distractores
distractores <- c()

# Distractor 1: mediana
mediana_val <- round(median(calificaciones), 1)
if(mediana_val != media_correcta) {
  distractores <- c(distractores, mediana_val)
}

# Distractor 2: valor maximo
max_val <- max(calificaciones)
if(max_val != media_correcta) {
  distractores <- c(distractores, max_val)
}

# Distractor 3: promedio de extremos
extremos_val <- round((min(calificaciones) + max(calificaciones)) / 2, 1)
if(extremos_val != media_correcta) {
  distractores <- c(distractores, extremos_val)
}

# Completar con valores aleatorios si es necesario
while(length(distractores) < 4) {
  nuevo_val <- round(runif(1, 2.0, 5.0), 1)
  if(!(nuevo_val %in% c(distractores, media_correcta))) {
    distractores <- c(distractores, nuevo_val)
  }
}

## TRANSFORM TO SINGLE CHOICE
questions <- c(media_correcta, distractores[1:4])
solutions <- c(TRUE, rep(FALSE, 4))

o <- sample(1:5)
questions <- questions[o]
solutions <- solutions[o]
```

Question
========
In the semester of `r materia`, `r estudiante` obtained the following grades in `r num_calificaciones` exams:

`r paste(calificaciones, collapse = ", ")`

What is the arithmetic mean of these grades?

```{r questionlist, echo = FALSE, results = "asis"}
answerlist(questions, markup = "markdown")
```

Solution
========
To calculate the arithmetic mean of `r estudiante`'s grades, we must:

1. Add all the grades: `r paste(calificaciones, collapse = " + ")` = `r suma_total`

2. Divide the sum by the number of grades: `r suma_total` / `r num_calificaciones` = `r media_correcta`

Therefore, the arithmetic mean is `r media_correcta`.

```{r solutionlist, echo = FALSE, results = "asis"}
answerlist(ifelse(solutions, "Correct", "Incorrect"), markup = "markdown")
```

Meta-information
================
extype: schoice
exsolution: `r mchoice2string(solutions, single = TRUE)`
exname: Arithmetic Mean Grades
